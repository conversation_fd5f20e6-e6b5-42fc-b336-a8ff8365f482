<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="Content-Security-Policy" content="media-src https: data:;">
    <% if("arihant".equals(session["entryController"])){%>
    <link rel="icon"  href="${assetPath(src: 'arihant/favicon.png')}" type="image/x-icon">
    <%}%>
    <% if("radianbooks".equals(session["entryController"])){%>
    <link rel="shortcut icon" href="${assetPath(src: 'radianbooks/radianbooks-logo.png')}" type="image/x-icon"/>
    <%}%>
    <% if("oswaal".equals(session["entryController"])){%>
    <link rel="icon" href="${assetPath(src: 'oswaal/favicon.ico')}" type="image/x-icon"/>
    <%}%>
    <% if("books".equals(session["entryController"])){%>
    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">
    <%}%>
    <%if("true".equals(session["commonWhiteLabel"])){%>
    <link rel="icon"  href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}" type="image/x-icon">
    <%}%>
    <% if("libwonder".equals(session["entryController"])){%>
    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">
    <%} else {%>
    <link rel="icon" href="${assetPath(src: "favicons/${session['entryController']}.png")}" type="image/x-icon">
    <%}%>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@100..900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp&display=swap" async>
    <link rel="stylesheet" href="/assets/bookgpt/bookgpt.css">
    <%if("true".equals(session["commonWhiteLabel"])){%>
     <asset:stylesheet href="whitelabel/override_style.css" async="true"/>
    <!-- Dynamic CSS Variables -->
    <style>
    :root {
        --theme-color: ${session["themeColor"]};
    }
    <%= session["customStyles"]?.replaceAll("&#64;", "@") %>
    #loginOpen .modal-content, #signup .modal-content, #forgotPasswordmodal .modal-content{
        flex-direction: column !important;
    }
    </style>
    <%}%>
    <style>
    .highlight {
        background-color: yellow;
        transition: background-color 0.3s ease;
        transform: translateZ(0);
        will-change: background-color;
    }
    body{background: #f8f9fa !important}
    .bookgpt .sections .pdfViewer{background: #f8f9fa !important}
    #defaultPromptPanel{background:#f8f9fa !important}
    .bookgpt .sections .chatViewer .chatOptions{background:#f8f9fa !important}
    .testseriesDisclaimer{
        color: #999;
        font-size: 12px;
        display: none;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
    }
    .botMessage img {
        width: 100%;
        overflow: scroll;
        object-fit: contain;
    }
    @media (max-width: 768px){
        .testseriesDisclaimer{
            font-size: 13px;
        }
    }

    /* Tablet-specific styles to ensure buttons are visible */
    @media (min-width: 769px) and (max-width: 1024px) {
        /* Ensure AI tutor button is visible on tablets */
        #openBookGPT {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative !important;
            z-index: 1000 !important;
        }

        /* Ensure Read button is visible when needed on tablets */
        #readBookBtn {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative !important;
            z-index: 1000 !important;
        }

        /* Make sure mobile bottom div is visible on tablets */
        .mobBottomDiv {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* Ensure mobile PDF viewer is properly displayed on tablets */
        .mobileViewer {
            display: block !important;
            visibility: visible !important;
        }

        /* Ensure mobile chat viewer is properly positioned on tablets */
        .mobileChatViewer {
            display: block !important;
            visibility: visible !important;
        }

        /* Make sure the CTA button is properly sized for tablets */
        .cta-button {
            font-size: 1.1em !important;
            padding: 12px 25px !important;
            min-height: 50px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        /* Ensure proper spacing and visibility for tablet layout */
        .bookgpt .sections {
            display: block !important;
        }

        /* Make sure panel containers don't interfere on tablets */
        .panel-container {
            display: none !important;
        }

        /* Ensure buttons are always clickable on tablets */
        #openBookGPT, #readBookBtn {
            pointer-events: auto !important;
            touch-action: manipulation !important;
            -webkit-touch-callout: none !important;
            -webkit-user-select: none !important;
            -khtml-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
            user-select: none !important;
        }

        /* Improve button tap targets for tablets */
        .cta-button, #openBookGPT, #readBookBtn {
            min-height: 48px !important;
            min-width: 48px !important;
        }

        /* Ensure proper z-index for tablet buttons */
        .mobBottomDiv, #openBookGPT, #readBookBtn {
            z-index: 1001 !important;
            position: relative !important;
        }

        /* Fix for tablet landscape mode */
        @media (min-width: 769px) and (max-width: 1024px) and (orientation: landscape) {
            #openBookGPT {
                bottom: 10px !important;
                right: 10px !important;
            }

            .mobBottomDiv {
                padding: 8px !important;
                margin: 5px !important;
            }
        }
    }
    </style>
    <asset:stylesheet href="wonderslate/vendors.min.css" async="true" media="all"/>
    <script src="https://code.jquery.com/jquery-1.12.4.min.js" crossorigin="anonymous"></script>
    <script src="/assets/wonderslate/vendors.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js" defer></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.4.0/styles/default.min.css">
    <script src="https://unpkg.com/@dotlottie/player-component@latest/dist/dotlottie-player.mjs" type="module"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
    <script src="/assets/bookGPTScripts/resizeScreens.js"></script>
    <script src="https://www.youtube.com/iframe_api"></script>
    <title>${title}</title>
    <script>
        const GPT_API_URL = "${gptServer}"
        let gptBookId = '${params.bookId}'
        let gptChapterId = '${params.chapterId}'
        let gptResId = '${params.resId}'
        console.log("gptBookId",gptBookId)
        console.log("gptChapterId",gptChapterId)
        console.log("gptResId",gptResId)
        let namespace = gptChapterId+"_"+gptResId
        let chapterId = gptChapterId;
        let resId=""
        let prevResId = ""
        const encryptedKey = '${encryptPdfKey}'
        const hasBookAccess = '${hasBookAccess}'
        const previewMode = '${previewMode}'
        const chaptersList = JSON.parse('${chapters}'.replace(/&#92;/g,'\\').replace(/&quot;/g,'"'))
        const bookLevelPrompt = '${bookLevelPrompt}'
        const defaultPromptsArray = '${defaultPrompts}'
        const bookTitle = '${bookTitle}'
        const eBookPrice = '${eBookPrice}'
        let freeTokenCount  = Number('${freeTokenCount}')
        let paidTokenCount = Number('${paidTokenCount}')
        const showNotice = '${showNotificaiton}'
        const bookPriceDtls = JSON.parse('${prices}'.replace(/&quot;/g,'"').replace(/&#92;/g, '\\'))
        let basePrompts = []
        let username =''
        let profilePic = ''
        let siteName="";
        const bookType = "${bookType}"
        const purchasedGPT = "${purchasedGPT}";
        const showUpgrade = "${showUpgrade}";
        <%if("true".equals(session["prepjoySite"]) || "true".equals(session["commonWhiteLabel"])){%>
        siteName="${session['siteName']}";
        <%}else{%>
        siteName="${session['entryController']}";
        <% }%>
        <%if(session["userdetails"]!=null){%>
        username = '${session["userdetails"].username}'
        username = username.replace("&#64;",'@')
        profilePic = '${session["userdetails"].profilepic}'
        userId = '${session["userdetails"].id}'
        <%}%>
        let loggedInUser = false;
        <sec:ifLoggedIn>
        loggedInUser = true;
        </sec:ifLoggedIn>
        let isContentsLocked = false;
        let isOffline = false;
        const showSnapshot = "${showSnapshot}";
        const showMcq = "${showMcq}";
        const showQa = "${showQa}";
        const showsnapPdf = "${showPdf}";
        const isTeacher = "${isTeacher}"
        let isSnapAvl = false;
        let isEpub = checkIfEpub();
        let bookFileType = "pdf";
        let loginUser = loggedInUser
        let suggestedVideos = []
        let skipDefaultHandler = false;
        const bookLang = "${bookLang}"
        const enableToken = "${enableToken}"
        let isTestSeries = false;
        let gptSiteId = "${session["siteId"]}";
        let enableCompiler = "${enableCompiler}"
        let gptcustomloader = "${gptcustomloader}"
        let gptloaderpath = "${gptloaderpath}"
        let gptloaderName = "${gptloaderName}"

        if((showsnapPdf!="true" || showsnapPdf==null) && showMcq=="true"){
            isTestSeries = true
        }
        if(showSnapshot=="true" || showMcq=="true" || showQa=="true"){
            isSnapAvl = true
        }

        function checkIfEpub(){
            const firstLink = chaptersList[0].link
            if(firstLink.endsWith('.epub') || firstLink.endsWith('.xhtml')){
                return true
            }
            return false
        }
        function snapStateChecker(){
            return {
                showSnapshot,
                showMcq,
                showQa,
                showsnapPdf
            }
        }
        function hideAppLoader(){
            const loader = document.getElementById('loader');
            loader.style.display = 'none';
            if(mobileView){
                const isTooltp = getCookie("mobgpt-tooltip")
                if(!isTooltp){
                    if(document.querySelector('.btn-tooltip')){
                        document.querySelector('.btn-tooltip').style.display = 'block'
                        setTimeout(()=>{
                            document.querySelector('.btn-tooltip').style.display = 'none'
                            setCookie("mobgpt-tooltip",true,30)
                        }, 5000)
                    }
                }
            }
        }
        function showAppLoader(sub){
            const loader = document.getElementById('loader');
            loader.style.display = 'flex';
            loader.style.flexDirection = 'column';
        }

        function updateChapterDropDown(groupedData){
            let resListHTML = ""
            const dropdownButton = document.querySelector('.dropdown-button');
            const dropdownMenu = document.querySelector('.dropdown-menu');
            const dropdownValue = document.getElementById('dropDownValue')
            const dropdownArrow = document.getElementById('dropdownArrow')
            const sortedChapters = Object.entries(groupedData)
                .sort(function (a, b) {
                    return a[1].sortOrder - b[1].sortOrder;
                });

            for (var i = 0; i < sortedChapters.length; i++) {
                var tempchapterId = sortedChapters[i][0];
                var chapter = sortedChapters[i][1];

                chapter.resources.forEach(function (resource) {
                    if(resource.resType==="Notes"  && (resource.link.endsWith('.pdf') || isEpub)){
                        if(hasBookAccess=="true"|| chapter.previewChapter=="true"){
                            if(resource.resId==gptResId){
                                resListHTML+= "<li class='dropdown-item chapterHighlight' data-chapterId='"+chapter.chapterId+"' data-resId='"+resource.resId+"'>"+resource.name+"</li>"
                            }else{
                                resListHTML+= "<li class='dropdown-item' data-chapterId='"+chapter.chapterId+"' data-resId='"+resource.resId+"'>"+resource.name+"</li>"
                            }
                        }else{
                            resListHTML+= "<li class='dropdown-item' data-locked=true data-chapterId='"+chapter.chapterId+"'><i class='fa-solid fa-lock'></i>"+resource.name+"</li>"
                        }

                    }
                })
            }
            dropdownMenu.innerHTML = resListHTML
            if(document.querySelector("[data-resId='"+gptResId+"']")){
                dropdownValue.innerHTML = document.querySelector("[data-resId='"+gptResId+"']").textContent.trim()
            }
            const dropdownItems = document.querySelectorAll('.dropdown-item');
            dropdownButton.addEventListener('click', function(e) {
                e.preventDefault()
                dropdownMenu.classList.toggle('show');
                dropdownArrow.classList.toggle('rotateArrowDown')
                dropdownArrow.classList.toggle('rotateArrowUp')
            });

            dropdownItems.forEach(function(item) {
                item.addEventListener('click', function() {
                    chapterId = item.getAttribute("data-chapterId")
                    const lockedItem = item.getAttribute("data-locked")
                    if(lockedItem=="true" && (!groupedData[chapterId].previewChapter || groupedData[chapterId].previewChapter=="false" )
                        && (previewMode!="false" || previewMode=="true")){
                        showChapterLockedModal()
                        return
                    }
                    prevResId = gptResId
                    dropdownValue.innerHTML = this.textContent;
                    dropdownMenu.classList.remove('show');
                    dropdownArrow.classList.toggle('rotateArrowDown')
                    dropdownArrow.classList.toggle('rotateArrowUp')
                    resId = item.getAttribute("data-resid")
                    namespace = chapterId+"_"+resId
                    dropdownItems.forEach(function(subitem) {
                        subitem.classList.remove('chapterHighlight')
                    });
                    item.classList.add('chapterHighlight')
                    if(prevResId!=resId){
                        history_for_llm = []
                        globalChatHistory=[]
                        getPDF(resId,chapterId)
                        getPromptsList()
                        const url = new URL(window.location.href);
                        url.searchParams.set('chapterId', chapterId);
                        url.searchParams.set('resId', resId);
                        window.history.pushState({}, '', url);
                        isPdfProcessed = false;
                        isdoubtInputEntered = false;
                        skipDefaultHandler = true
                        isTyping.style.display='none'
                        inProgressQue = [];
                        if(document.querySelector('.prompts_dropdown-content-list')){
                            document.querySelector('.prompts_dropdown-content-list').innerHTML = ""
                        }
                    }
                });
            });

            window.addEventListener('click', function(e) {
                if (!dropdownButton.contains(e.target)) {
                    dropdownMenu.classList.remove('show');
                    dropdownArrow.classList.add('rotateArrowDown')
                    dropdownArrow.classList.remove('rotateArrowUp')
                }
            });
        }
        let mobileView = false;
        function isMobile() {
            return window.innerWidth <= 1024; // Updated to include tablets
        }
        function isTablet() {
            return window.innerWidth > 768 && window.innerWidth <= 1024;
        }
        if (isMobile()) {
            mobileView = true;
        }
        function isIOS() {
            const ua = navigator.userAgent || navigator.vendor || window.opera;
            return /iPad|iPhone|iPod/.test(ua) && !window.MSStream;
        }
    </script>
</head>
<body style="overflow: hidden">
<sec:ifNotLoggedIn>
    <%
        boolean otpLogin=false;
        if ("true".equals(session['enableOTPLogin'])&&request.getServerName().indexOf("publish.")==-1){
            otpLogin=true
        }
    %>
    <%if(!otpLogin){%>
    <g:render template="/books/signIn"></g:render>
    <%}else if(otpLogin){%>
    <g:render template="/privatelabel/otpOnlyLogin"></g:render>
    <%}%>
</sec:ifNotLoggedIn>
<div id="loader" class="loader">
    <dotlottie-player src="https://lottie.host/a464fcaf-6362-4ede-b647-7b48911a2375/LJJIok88LW.json" background="transparent" speed="1" style="width: 300px; height: 300px;" loop autoplay></dotlottie-player>
    <div class="introText">
        <h3 id="loaderSub" style="margin-top: 12px;justify-content: center">
            <%if("Yes".equals(gptcustomloader) && gptloaderpath !=null){%>
                <img src="/privatelabel/showPrivatelabelImage?siteId=${""+session["siteId"]}&fileName=${gptloaderpath}" style="width: 120px;" />
            <%}else {%>
                <img src="/assets/resource/ibookgpt-logo-light.svg" style="width: 120px;" />
            <%}%>
        </h3>
        <p style="font-size:14px;margin-top: 10px;text-align: center ">Your Book with GPT is being prepared, thank you for waiting...</p>
    </div>

</div>
<%
    String userAgent = request.getHeader("User-Agent");
    // Updated to include tablets - iPad and Android tablets
    Boolean isMobile = userAgent =~ /Mobile|Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Tablet/;
%>
<%
    boolean showToken = true
    if(freeTokenCount>300){
        showToken = false
    }
%>
<script>
    let showTokens = "${showToken}";
</script>
<div class="warning-dialog">
    <i class="fa-solid fa-xmark warning-close" onclick="closeWarningDialog()"></i>
    <i class="fa-solid fa-triangle-exclamation warning-icon"></i>
    <p class="warning-text">Unstable internet connection detected.</p>
</div>
<div class="bookgpt">
    <div class="header">
        <div class="chapterWrap">
            <button class="backButton" id="backButton"><i class="fa-solid fa-chevron-left"></i></button>
            <% if (isMobile) { %>
                <button class="readBookBtn" id="readBookBtn" style="display:none;"><i class="fa-solid fa-chevron-left"></i> Back</button>
            <% } %>
            <div class="chaptersList">
                <div class="custom-dropdown">
                    <button class="dropdown-button">
                        <span id="dropDownValue">Select chapter</span>
                        <i class="fa-solid fa-angle-down rotateArrowDown" id="dropdownArrow" style="font-size: 15px;"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li class="dropdown-item" data-chapterId="1">Option 1</li>
                        <li class="dropdown-item" data-chapterId="2">Option 2</li>
                        <li class="dropdown-item" data-chapterId="3">Option 3</li>
                    </ul>
                </div>
            </div>
            <% if (isMobile && (showNotificaiton == "true" || showNotificaiton=="Yes" ||showNotificaiton=="yes" && showNotificaiton != null)) { %>
                <i class="fa-solid fa-circle-info mobNotice" id="mobNotice"></i>
            <% } %>
        </div>
        <%if(hasBookAccess && !isMobile && showToken){%>
        <div class="tokensSec">
            <div class="tokensSec-counts">
                <p>Doubts: </p>
                <p>
                    <span>Free: </span>
                    <span id="freeTokenCountItem"></span>
                </p>
                <p>
                    <span>Paid: </span>
                    <span id="paidTokenCountItem"></span>
                </p>
            </div>
            <a href="/whitelabel/recharge" class="superChargeBtn">Recharge <i class="fa-solid fa-bolt"></i></a>
        </div>
        <%}%>
        <%if(previewMode  && !hasBookAccess){%>
        <div class="gptBuyNowBtnWrap" style="display:flex;align-items: center">
            <sec:ifNotLoggedIn>
            <a href="javascript:openLoginModal()" style="font-size: 12px;margin-right: 5px;">Login</a>
            </sec:ifNotLoggedIn>
            <button class="gptBuyNowBtn" id="gptBuyNowBtn" onclick="openBookDtlPage()">Buy Now</button>
        </div>
        <%}%>
    </div>

    <div class="sections resizable-x">

        <% if (!isMobile) { %>
        <div class="panel-container">

            <div class="panel-left">
                <g:render template="bookgptPDFViewer"></g:render>
            </div>
            <div class="splitter" title="HOLD AND MOVE LEFT OR RIGHT TO ADJUST THE SCREEN.">
            </div>
            <div class="panel-right">
                <g:render template="bookgptChatViewer"></g:render>
            </div>
        </div>
        <% } else { %>
            <g:render template="bookgptPDFViewerMobile"></g:render>
            <g:render template="bookgptChatViewerMobile"></g:render>
        <% } %>
    </div>
    <div id="feedbackModal" class="feedbackModal">
        <div class="modal-content">
            <div id="feedbackContent"></div>
        </div>
    </div>
    <div id="videoModal" class="videoModal">
        <div class="videoModal-modal-content">
            <span class="videoModal-closeModalBtn" id="videoModal-closeModalBtn" onclick=closeytMdal()>&times;</span>
            <div class="video-wrapper">
                <iframe id="youtubeVideo" src="" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>
            </div>
        </div>
    </div>
</div>

<% if(enableCompiler) {%>
    <g:render template="codeRunner"></g:render>
<% }%>

<div id="selection-box"></div>
<div id="printableDiv"></div>
<script>
    if(!mobileView){
        $(".panel-left").resizable({
            handleSelector: ".splitter",
            resizeHeight: false
        });
    }
</script>
<script src="/assets/bookGPTScripts/languageDetection.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="/assets/bookGPTScripts/bookGPTPdfViewer.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/13.0.1/marked.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.4.0/highlight.min.js"></script>
<script src="/assets/bookGPTScripts/bookGPTChatViewer.js"></script>
<script src="/assets/bookGPTScripts/testSeriesChat.js"></script>
<script>
    document.addEventListener('DOMContentLoaded',(e)=>{
        // Tablet-specific fixes to ensure buttons are visible
        if(isTablet()){
            // Force mobile view behavior for tablets
            mobileView = true;

            // Ensure AI tutor button is visible on tablets
            const openBookGPTBtn = document.getElementById('openBookGPT');
            if(openBookGPTBtn){
                openBookGPTBtn.style.display = 'flex';
                openBookGPTBtn.style.visibility = 'visible';
                openBookGPTBtn.style.opacity = '1';
            }

            // Ensure Read button is available for tablets
            const readBookBtn = document.getElementById('readBookBtn');
            if(readBookBtn){
                readBookBtn.style.display = 'block';
                readBookBtn.style.visibility = 'visible';
                readBookBtn.style.opacity = '1';
            }

            // Make sure mobile bottom div is visible
            const mobBottomDiv = document.querySelector('.mobBottomDiv');
            if(mobBottomDiv){
                mobBottomDiv.style.display = 'flex';
                mobBottomDiv.style.visibility = 'visible';
            }
        }

        if(isTestSeries){
            if(document.querySelector('.fullScreenBtn')){
                document.querySelector('.fullScreenBtn').style.display = "none";
            }
            if(document.getElementById('openBookGPT')){
                document.getElementById('openBookGPT').style.display = "none"
            }
            if(document.querySelector('.testseriesDisclaimer')){
                document.querySelector('.testseriesDisclaimer').style.display = "flex"
            }
        }
        if(enableToken=="true"){
            if(!isMobile() || (isMobile() && hasBookAccess)){
                document.querySelector('.tokensSec').style.display = "flex"
            }
        }
        if(document.referrer=="" && loggedInUser){
            localStorage.setItem('referrer',window.location.host+"/wsLibrary/myLibrary")
        }else if(document.referrer=="" && !loggedInUser){
            localStorage.setItem('referrer',window.location.origin+"/")
        }else{
            localStorage.setItem('referrer',document.referrer)
        }

        <%if(hasBookAccess && showToken && "true".equals(enableToken)){%>
            updateTokenCount()
        <%}%>
    })

    const mobNotice = document.getElementById('mobNotice')

    if(mobNotice && !isSnapAvl){
        mobNotice.addEventListener('click',()=>{
            feedbackContent.innerHTML = "<div>" +
                "<i class='fa-solid fa-xmark' id='noticeClose' onclick='closeNotice()'></i>" +
                "<p class='noticeMsg mob'>This is not part of iBookGPT. This material is provided for the convenience of the user for ready reference.</p>" +
                "</div>"
            openModal()

        })
    }

    function closeNotice(){
        modal.style.display = 'none';
        modal.classList.remove('fade-out');
        isResponseComplete = true
        isTyping.style.display = 'none';
    }
    function updateTokenCount(){
        const freeTokenCountItem = document.getElementById('freeTokenCountItem')
        const paidTokenCountItem = document.getElementById('paidTokenCountItem')
        freeTokenCountItem.innerHTML = freeTokenCount
        paidTokenCountItem.innerHTML = paidTokenCount
    }
    function openBookDtlPage(){
        let bookRedirectionURL=""
        if(bookPriceDtls.length>1){
            const hasEbookGPTUpgrade = bookPriceDtls.some(price => price.bookType === "ebookGPTUpgrade");
            if(isContentsLocked && hasEbookGPTUpgrade){
                bookRedirectionURL= "/" + bookTitle.replaceAll(' ','-') + "/ebook-details?siteName="+siteName+"&bookId=" + gptBookId +"&preview=true&bookType=ebookGPTUpgrade&addToCart=true"
            }else{
                bookRedirectionURL= "/" + bookTitle.replaceAll(' ','-') + "/ebook-details?siteName="+siteName+"&bookId=" + gptBookId +"&preview=true"
            }
        }else{
            const bookType = bookPriceDtls[0].bookType
            bookRedirectionURL= "/" + bookTitle.replaceAll(' ','-') + "/ebook-details?siteName="+siteName+"&bookId=" + gptBookId +"&preview=true&bookType="+bookType+"&addToCart=true"
        }
        window.open(bookRedirectionURL, '_blank');
    }
    function openRechargePage(){
        let bookRedirectionURL=""
        bookRedirectionURL= "/whitelabel/recharge"
        window.open(bookRedirectionURL, '_blank');
    }

    function goBack(){
        const refLink = localStorage.getItem('referrer')
        if(refLink && refLink!=""){
            if(refLink.includes("ebook-details")){
                window.close();
            }else{
                window.location.href = refLink;
            }
        }
    }
    document.getElementById('backButton').addEventListener('click',goBack)

    function addTextToDoubts(str){
        document.getElementById('chatInput').value = str
        if(mobileView){
            showMobileChat()
        }else{
            document.getElementById('chatInput').focus()
        }
    }

    function addToCompiler(str){
        if(enableCompiler == "true"){
            injectCodeToEditor(str)
        }
    }

    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }

    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for(var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    window.addEventListener('load', function() {
        const sectionDiv = document.querySelector('.sections')
        const height = sectionDiv.offsetHeight;
        const bookGPTReaderDiv = document.getElementById('bookGPTReader')

        if(bookGPTReaderDiv && mobileView){
            if(isIOS()){
                bookGPTReaderDiv.style.height = (height - 70 - 60 - 40) +'px'
            }else{
                bookGPTReaderDiv.style.height = (height - 70 - 60) +'px'
            }
        }
        var pdfViewerValue = getCookie('pdfViewerWidth');
        if (pdfViewerValue &&  document.querySelector('.panel-left')) {
            document.querySelector('.panel-left').style.width=pdfViewerValue+'px'
        }
    });
    window.addEventListener('beforeunload', function (event) {
        localStorage.removeItem('referrer')
        const pdfFlex = document.querySelector('.panel-left')
        if(pdfFlex){
            const width = pdfFlex.offsetWidth;
            setCookie('pdfViewerWidth',width)
        }
    });

    // Handle window resize for tablets (orientation changes)
    window.addEventListener('resize', function() {
        // Update mobile view status on resize
        const wasMobile = mobileView;
        mobileView = isMobile();

        // If we switched from desktop to tablet/mobile or vice versa
        if(wasMobile !== mobileView && isTablet()){
            // Ensure buttons remain visible on tablets after orientation change
            setTimeout(() => {
                const openBookGPTBtn = document.getElementById('openBookGPT');
                if(openBookGPTBtn){
                    openBookGPTBtn.style.display = 'flex';
                    openBookGPTBtn.style.visibility = 'visible';
                    openBookGPTBtn.style.opacity = '1';
                }

                const readBookBtn = document.getElementById('readBookBtn');
                if(readBookBtn){
                    readBookBtn.style.visibility = 'visible';
                    readBookBtn.style.opacity = '1';
                }
            }, 100);
        }
    });
    function openLoginModal(){
        forceRegisterMode = "bookGPTLogin"
        loginOpen()
    }

    if(!mobileView){
        const fullScreen = document.querySelector('.fullScreenBtn')

        fullScreen.addEventListener('click',(e)=>{
            const panelLeftDiv = document.querySelector('.panel-left')
            const chatItem = fullScreen.getAttribute('data-chat')
            if(panelLeftDiv){
                if(chatItem=="open"){
                    hideChatWindow(fullScreen, panelLeftDiv)
                }else if(chatItem=="close"){
                    showChatWindow(fullScreen, panelLeftDiv)
                }
            }
        })
    }

    function hideChatWindow(fullScreen, panelLeftDiv){
        if(!isMobile()){
            panelLeftDiv.style.width = "100%";
            panelLeftDiv.style.maxWidth = "100%";
            if(fullScreen){
                fullScreen.innerHTML = "<i class='fa-regular fa-comment'></i> AI Tutor"
                fullScreen.setAttribute("data-chat","close")
                if(isTestSeries){
                    fullScreen.style.display = "none"
                }
            }
        }else{
            hideMobileChat()
        }

        document.getElementById('showClearChatBtn').style.display ='none';
    }

    function showChatWindow(fullScreen, panelLeftDiv){
        if(!isMobile()){
            panelLeftDiv.style.width = "70%";
            panelLeftDiv.style.maxWidth = "95%";
            if(fullScreen){
                fullScreen.innerHTML = !isTestSeries ? "<i class='fa-solid fa-expand'></i> Full Screen" : "<i class='fa-solid fa-expand'></i> Close Chat"
                fullScreen.setAttribute("data-chat","open")
                fullScreen.style.display = "flex"
            }
        }else{
            showMobileChat()
        }
        document.getElementById('showClearChatBtn').style.display ='block';
    }

    async function showChatWindowCB(actionObj, actionType, actionId, language, languageVal){
        const fullScreen = document.querySelector('.fullScreenBtn')
        const panelLeftDiv = document.querySelector('.panel-left')
        const openBookGPTBtnTemp = document.getElementById('openBookGPT')

        showChatWindow(fullScreen, panelLeftDiv)
        if(checkTokens() || actionType=="history"){
            if(currentMcqObjId!=actionObj.id){
                pageNo = 0;
                clearChat()
                history_for_llm  = []
                globalChatHistory = []
                showGptLoader()
                currentMcqObjId = actionObj.id
                await getChatHistory()
                hideGptLoader()
            }
            currentMcqObjId = actionObj.id
            currentMcqObj = actionObj
            if(actionType=="history"){
                return;
            }else{
                interactWithTest(actionObj, actionType, actionId, null, language, languageVal)
            }
        }else{
            showBuyPopup()
        }
    }

    function hideChatWindowCB(){
        const fullScreen = document.querySelector('.fullScreenBtn')
        const panelLeftDiv = document.querySelector('.panel-left')
        hideChatWindow(fullScreen, panelLeftDiv)
        if (controller) {
            controller.abort();
            controller = null;
        }
    }
    const showOfflineDialog = (msg)=>{
        const warningDialog = document.querySelector('.warning-dialog')
        const warningIcon = document.querySelector('.warning-icon')
        const warningText = document.querySelector('.warning-text')
        if(mobileView){
            warningDialog.style.top = '45px'
        }else{
            warningDialog.style.top = '33px'
        }
        warningText.textContent = msg
        warningIcon.classList.add('fa-triangle-exclamation')
        warningIcon.classList.remove('fa-bolt')
        warningDialog.style.background = "#ED6114"
    }

    const showOnlineDialog = ()=>{
        const warningDialog = document.querySelector('.warning-dialog')
        const warningIcon = document.querySelector('.warning-icon')
        const warningText = document.querySelector('.warning-text')
        warningText.textContent = "You're back online!"
        warningIcon.classList.add('fa-bolt')
        warningIcon.classList.remove('fa-triangle-exclamation')
        warningDialog.style.background = "#06c44c"
        setTimeout(()=>{
            warningDialog.style.top = '-150%'
        },5000)
    }

    const closeWarningDialog = ()=>{
        const warningDialog = document.querySelector('.warning-dialog')
        warningDialog.style.top = '-150%'
    }
    window.addEventListener('online', () => {
        isOffline = false;
        showOnlineDialog()
    })
    window.addEventListener('offline', () => {
        isOffline = true;
        showOfflineDialog("You're offline. Please check your internet.")
    });

    if ('connection' in navigator) {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        if (connection.effectiveType === '2g' || connection.effectiveType === '3g' || connection.saveData  || connection.effectiveType.includes('2g') || connection.effectiveType.includes('3g')) {
            showOfflineDialog("Your internet connection is slow.")
        } else {
            showOnlineDialog()
        }

        connection.addEventListener('change', () => {
            if (connection.effectiveType === '2g' || connection.effectiveType === '3g' || connection.saveData || connection.effectiveType.includes('2g') || connection.effectiveType.includes('3g')) {
                showOfflineDialog("Your internet connection is slow.")
            } else {
                if(!isOffline){
                    showOnlineDialog()
                }
            }
        });
    } else {
        console.log("Network Information API is not supported by this browser.");
    }

    if(isSnapAvl){

        if(mobileView){
            document.querySelector('.mobNotice').style.display = "none"
        }else{
            document.querySelector('.noticeMsg').style.display = "none"
        }
    }
    document.addEventListener('keydown', event => {
        if (event.ctrlKey || event.metaKey && (event.key === 's' || event.key === '3' || event.key === '#')) {
            document.body.style.background = "#000"
            document.body.innerHTML = "<div style='display: flex;flex-direction: column;justify-content: center;align-items: center'>" +
                "<h1 style='color: #fff'>Oops! Saving this webpage is not allowed.</h1> " +
                "<p style='color: #fff'>Reload the page if you're facing any issues</p>" +
                "</div>";
            event.preventDefault();
            event.stopPropagation();
            return false;
        }
        if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === '3') {
            document.body.style.background = "#000"
            document.body.innerHTML = "<div style='display: flex;flex-direction: column;justify-content: center;align-items: center'>" +
                "<h1 style='color: #fff'>Oops! Saving this webpage is not allowed.</h1> " +
                "<p style='color: #fff'>Reload the page if you're facing any issues</p>" +
                "</div>";
            event.preventDefault();
            event.stopPropagation();
            return;
        }
        if((event.ctrlKey || event.metaKey && event.shiftKey)){
            document.body.style.background = "#000"
            document.body.innerHTML = "<div style='display: flex;flex-direction: column;justify-content: center;align-items: center'>" +
                "<h1 style='color: #fff'>Oops! Saving this webpage is not allowed.</h1> " +
                "<p style='color: #fff'>Reload the page if you're facing any issues</p>" +
                "</div>";
            event.preventDefault();
            event.stopPropagation();

            return false;
        }
    },false);
</script>
</body>
</html>
